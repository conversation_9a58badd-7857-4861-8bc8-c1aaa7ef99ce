import 'package:business_app/ui/user_profiles/components/about/my_bio.dart';
import 'package:business_app/ui/user_profiles/components/pages/user_shops_page.dart';
import 'package:business_app/ui/user_profiles/components/pages/user_posts_page.dart';
import 'package:business_app/ui/user_profiles/components/pages/user_products.dart';
import 'package:business_app/ui/user_profiles/edit_profile_page.dart';
import 'package:business_app/bloc/profile_bloc/profile_bloc.dart';
import 'package:business_app/bloc/profile_bloc/profile_event.dart';
import 'package:business_app/bloc/profile_bloc/profile_state.dart';
import 'package:business_app/skeletons/skeletons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MyProfilePage extends StatefulWidget {
  const MyProfilePage({super.key});

  @override
  State<MyProfilePage> createState() => _MyProfilePageState();
}

class _MyProfilePageState extends State<MyProfilePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _showNameInAppBar = false;

  final double profilePicRadius = 60.0;
  final double coverImageHeight = 200.0;
  final double profilePicOverlap = 30.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();

    // Load profile data
    context.read<ProfileBloc>().add(LoadProfileEvent());

    _scrollController.addListener(() {
      if (_scrollController.offset > 200 && !_showNameInAppBar) {
        setState(() {
          _showNameInAppBar = true;
        });
      } else if (_scrollController.offset <= 200 && _showNameInAppBar) {
        setState(() {
          _showNameInAppBar = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Helper method to format large numbers
  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  // Helper method to build background avatar when no image is available
  Widget _buildBackgroundAvatar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade400, Colors.purple.shade400],
        ),
      ),
      child: const Center(
        child: Icon(Icons.landscape, size: 80, color: Colors.white70),
      ),
    );
  }

  // Helper method to build profile avatar when no image is available
  Widget _buildProfileAvatar() {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, profileState) {
        return CircleAvatar(
          radius: 64,
          backgroundColor: Colors.grey.shade300,
          child: _buildProfileAvatarContent(),
        );
      },
    );
  }

  // Helper method to build profile avatar content (initials)
  Widget _buildProfileAvatarContent() {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, profileState) {
        // Get first letter of name for avatar
        String initials = '';
        if (profileState.name.isNotEmpty) {
          final nameParts = profileState.name.trim().split(' ');
          if (nameParts.length >= 2) {
            initials = '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
          } else {
            initials = nameParts[0][0].toUpperCase();
          }
        } else {
          initials = 'U'; // Default for User
        }

        return Text(
          initials,
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, profileState) {
        // Show skeleton while profile is loading
        if (profileState.status == ProfileStatus.loading) {
          return const ProfileSkeleton();
        }

        // Show error dialog if profile failed to load
        if (profileState.status == ProfileStatus.error) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            SkeletonErrorDialog.show(
              context,
              title: 'Something went wrong',
              message:
                  profileState.errorMessage ??
                  'We couldn\'t load your profile. Please try again.',
              onRetry: () {
                context.read<ProfileBloc>().add(LoadProfileEvent());
              },
              onDismiss: () {
                Navigator.of(context).pop();
              },
            );
          });
          return const ProfileSkeleton();
        }

        return Scaffold(
          body: NestedScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            headerSliverBuilder:
                (context, innerBoxIsScrolled) => [
                  SliverAppBar(
                    pinned: true,
                    actions: [
                      //without gradient
                      // IconButton(
                      //   icon: Icon(
                      //     Icons.search, //color: Colors.white
                      //   ),
                      //   onPressed: () {},
                      // ),
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const EditProfilePage(),
                            ),
                          );
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.black.withOpacity(0.5),
                                Colors.black.withOpacity(0.1),
                              ],
                              stops: [0.4, 1.0],
                              center: Alignment.center,
                              radius: 1.0,
                            ),
                          ),
                          child: Icon(
                            Icons.edit,
                            color: Colors.white,
                            // size: 20,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      //without gradient
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.black.withOpacity(0.5),
                              Colors.black.withOpacity(0.1),
                            ],
                            stops: [0.4, 1.0],
                            center: Alignment.center,
                            radius: 1.0,
                          ),
                        ),
                        child: Icon(
                          Icons.search,
                          color: Colors.white,
                          // size: 20,
                        ),
                      ),
                      SizedBox(width: 8),
                      //without gradient
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.black.withOpacity(0.5),
                              Colors.black.withOpacity(0.1),
                            ],
                            stops: [0.4, 1.0],
                            center: Alignment.center,
                            radius: 1.0,
                          ),
                        ),
                        child: IconButton(
                          icon: Icon(Icons.more_vert, color: Colors.white),
                          onPressed: () {
                            // Show the MoreIcon bottom sheet
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              builder: (BuildContext context) => MoreIcon(),
                            );
                          },
                        ),
                      ),
                      SizedBox(width: 8),
                      //without gradient
                      // IconButton(
                      //   icon: Icon(
                      //     Icons.more_vert, //color: Colors.white
                      //   ),
                      //   onPressed: () {
                      //     // Show the MoreIcon bottom sheet
                      //     showModalBottomSheet(
                      //       context: context,
                      //       backgroundColor: Colors.transparent,
                      //       builder: (BuildContext context) => MoreIcon(),
                      //     );
                      //   },
                      // ),
                    ],
                    expandedHeight:
                        coverImageHeight + profilePicRadius - profilePicOverlap,
                    flexibleSpace: FlexibleSpaceBar(
                      background: Stack(
                        children: [
                          Positioned(
                            top: 0,
                            left: 0,
                            right: 0,
                            height: coverImageHeight,
                            child: BlocBuilder<ProfileBloc, ProfileState>(
                              builder: (context, state) {
                                if (state.backgroundImageUrl.isNotEmpty) {
                                  return Image.network(
                                    state.backgroundImageUrl,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return _buildBackgroundAvatar();
                                    },
                                  );
                                } else {
                                  return _buildBackgroundAvatar();
                                }
                              },
                            ),
                          ),
                          Positioned(
                            left: 20,
                            bottom: 0,
                            //ORIGINAL WIDGET
                            /*child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: CircleAvatar(
                            radius: profilePicRadius,
                            backgroundImage: NetworkImage(
                              'https://cdni.pornpics.com/1280/1/367/25814960/25814960_003_bb2b.jpg',
                            ),
                          ),
                        ),*/
                            child: GestureDetector(
                              onTap: () {},
                              child: BlocBuilder<ProfileBloc, ProfileState>(
                                builder: (context, profileState) {
                                  return Stack(
                                    children: [
                                      CircleAvatar(
                                        radius: 66,
                                        backgroundColor: Colors.amber,
                                        child:
                                            profileState
                                                    .profileImageUrl
                                                    .isNotEmpty
                                                ? CircleAvatar(
                                                  radius: 64,
                                                  backgroundColor:
                                                      Colors.grey.shade300,
                                                  child: ClipOval(
                                                    child: Image.network(
                                                      profileState
                                                          .profileImageUrl,
                                                      width: 128,
                                                      height: 128,
                                                      fit: BoxFit.cover,
                                                      errorBuilder: (
                                                        context,
                                                        error,
                                                        stackTrace,
                                                      ) {
                                                        return _buildProfileAvatarContent();
                                                      },
                                                    ),
                                                  ),
                                                )
                                                : _buildProfileAvatar(),
                                      ),
                                      Positioned(
                                        bottom: 10,
                                        right: 12,
                                        child: Container(
                                          width: 16,
                                          height: 16,
                                          decoration: BoxDecoration(
                                            color: Colors.green,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.white,
                                              width: 2,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Insert card above tabs
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Flexible(
                                          child: BlocBuilder<
                                            ProfileBloc,
                                            ProfileState
                                          >(
                                            builder: (context, profileState) {
                                              return Text(
                                                profileState.name.isNotEmpty
                                                    ? profileState.name
                                                    : 'Loading...',
                                                style: const TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                                maxLines: 3,
                                                softWrap: true,
                                              );
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 4),
                                        Icon(
                                          Icons.verified,
                                          color: Colors.blue,
                                          size: 20,
                                        ),
                                        /* SizedBox(width: 12),
                                    OutlinedButton(
                                      onPressed: () {},
                                      style: OutlinedButton.styleFrom(
                                        side: BorderSide(color: Colors.grey),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                        backgroundColor: Colors.white
                                            .withOpacity(0.1),
                                      ),
                                      child: Text(
                                        'Follow',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),*/
                                      ],
                                    ),
                                    BlocBuilder<ProfileBloc, ProfileState>(
                                      builder: (context, profileState) {
                                        return Text(
                                          profileState.username.isNotEmpty
                                              ? '@${profileState.username}'
                                              : '@loading...',
                                          style: const TextStyle(
                                            color: Colors.grey,
                                            fontSize: 14,
                                          ),
                                        );
                                      },
                                    ),
                                    SizedBox(height: 8),
                                    BlocBuilder<ProfileBloc, ProfileState>(
                                      builder: (context, profileState) {
                                        return Text(
                                          profileState.bio.isNotEmpty
                                              ? profileState.bio
                                              : 'No bio available',
                                          style: const TextStyle(fontSize: 16),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),

                              // SizedBox(width: 10),
                              // OutlinedButton(
                              //   onPressed: () {},
                              //   style: OutlinedButton.styleFrom(
                              //     side: BorderSide(color: Colors.grey),
                              //     shape: RoundedRectangleBorder(
                              //       borderRadius: BorderRadius.circular(20),
                              //     ),
                              //     backgroundColor: Colors.white.withOpacity(0.1),
                              //   ),
                              //   child: Text(
                              //     'Follow',
                              //     style: TextStyle(fontWeight: FontWeight.bold),
                              //   ),
                              // ),
                            ],
                          ),

                          SizedBox(height: 8),

                          // Following and Customers
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              crossAxisAlignment: WrapCrossAlignment.center,
                              alignment: WrapAlignment.start,
                              children: [
                                SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: BlocBuilder<ProfileBloc, ProfileState>(
                                    builder: (context, profileState) {
                                      return Row(
                                        children: [
                                          Text(
                                            '${profileState.followingCount}',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          const Text(
                                            'Following',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey,
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Text(
                                            _formatCount(
                                              profileState.followersCount,
                                            ),
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          const Text(
                                            'Customers',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 8),
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton(
                              onPressed: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const EditProfilePage(),
                                  ),
                                );
                              },
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(
                                  //color: Colors.white
                                ),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                              child: const Text(
                                'Edit profile',
                                style: TextStyle(
                                  //color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          /* Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.call,
                                          size: 16,
                                          //color: Colors.blue,
                                        ),
                                        SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            'Call',
                                            style: TextStyle(
                                              fontSize: 16,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.favorite_border,
                                        size: 16,
                                        color: Colors.red,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        'Like',
                                        style: TextStyle(fontSize: 16),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(Icons.location_on, size: 16),
                                  SizedBox(width: 4),
                                  Text(
                                    'Open now',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.blue,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),*/
                        ],
                      ),
                    ),
                  ),

                  // Sticky tab bar
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: _TabBarDelegate(
                      TabBar(
                        controller: _tabController,
                        labelColor: Color(0xFF1DA1F2),
                        unselectedLabelColor: Colors.grey,
                        indicatorColor: Color(0xFF1DA1F2),
                        indicatorWeight: 5,
                        labelStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        unselectedLabelStyle: TextStyle(
                          fontWeight: FontWeight.normal,
                          fontSize: 16,
                        ),
                        tabs: const [
                          Tab(text: 'Products'),
                          Tab(text: 'Shops'),
                          Tab(text: 'Posts'),
                        ],
                      ),
                    ),
                  ),
                ],
            body: TabBarView(
              controller: _tabController,
              physics: BouncingScrollPhysics(),
              children: [
                //  _buildTabContent("Products"),
                //   _buildTabContent("Shops'),"),
                //   _buildTabContent("Posts" ),
                UserProducts(),
                UserShopsPage(),
                UserPostsPage(),
                // UserAllPostsPage(posts: []),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Sticky TabBar delegate
class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;
  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) =>
      false;
}

//more icon in the app bar
// Stateful class for the bottom sheet
class MoreIcon extends StatefulWidget {
  const MoreIcon({super.key});

  @override
  MoreIconState createState() => MoreIconState();
}

class MoreIconState extends State<MoreIcon> {
  bool _isHidden = false; // Example state for the switch

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: BouncingScrollPhysics(),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          // color: Colors.black.withOpacity(0.9),
          color: Theme.of(context).colorScheme.surface.withOpacity(0.94),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Top handle bar
            Container(
              margin: EdgeInsets.symmetric(vertical: 10),
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[700],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            // Bottom sheet options
            _buildOption('My Info', () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => MyBio()),
              );
            }),
            _buildOption('Business Stats', () {
              Navigator.pop(context);
              // Add report action
            }),

            // _buildOption('Add', Colors.white, () {
            //   Navigator.pop(context);
            //   // Add add action
            // }),
            _buildOption(
              'Hide Products',

              () {},
              trailing: Switch(
                value: _isHidden,
                onChanged: (value) {
                  setState(() {
                    _isHidden = value;
                  });
                  // Add hide content action
                },
                activeColor: Colors.blue,
                inactiveThumbColor: Colors.grey,
                inactiveTrackColor: Colors.grey[800],
              ),
            ),

            //original code
            /* _buildOption(
              'Send Profile to...',
              Colors.white,
              () {
                Navigator.pop(context);
                // Add send profile action
              },
              trailing: Icon(Icons.send, color: Colors.blue),
            ),
            */
            _buildOption('Send Profile to...', () {
              Navigator.pop(context);
              // Add send profile action
            }, trailing: Icon(Icons.send, color: Colors.blue)),
            _buildOption(
              'Copy Business URL',
              //trailing: Icon(Icons.send, color: Colors.blue),
              // Colors.white,
              () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    content: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.link),
                          SizedBox(width: 8),
                          Text('Link copied', style: TextStyle(fontSize: 16)),
                        ],
                      ),
                    ),
                    duration: Duration(seconds: 3),
                  ),
                );
                // Add share URL action
              },
            ),
            /* original code
            _buildOption('Copy Business URL', Colors.white, () {
              Navigator.pop(context);
              // Add share URL action
            }),
            */
            // Done button
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
              child: TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Done',
                  style: TextStyle(
                    //color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build each option
  Widget _buildOption(
    String text,
    // Color color,
    VoidCallback onTap, {
    Widget? trailing,
  }) {
    return ListTile(
      title: Text(
        text,
        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
      ),
      trailing: trailing,
      onTap: onTap,
    );
  }
}
